import React from 'react';
import { WidgetProps } from '@rjsf/utils';
import { Checkbox } from '@mui/material';

// Use the same icons as the right checkbox for consistency
export const CheckedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M9.26667 10.9231L11.9333 13.6154L21 5M19 13.6154V16.8462C19 18.0357 18.0449 19 16.8667 19L7.13333 19C5.95513 19 5 18.0357 5 16.8462V7.15385C5 5.96431 5.95513 5 7.13333 5H14" stroke="#27725B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

export const UnCheckedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M5 7V17C5 18.1046 5.89543 19 7 19H17C18.1046 19 19 18.1046 19 17V7C19 5.89543 18.1046 5 17 5H7C5.89543 5 5 5.89543 5 7Z" stroke="#27725B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const CustomCheckboxWidget: React.FC<WidgetProps> = ({
  id,
  value,
  disabled,
  readonly,
  onChange,
  onBlur,
  onFocus,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.checked);
  };

  const handleBlur = () => {
    onBlur && onBlur(id, value);
  };

  const handleFocus = () => {
    onFocus && onFocus(id, value);
  };

  return (
    <Checkbox
      id={id}
      checked={value || false}
      disabled={disabled || readonly}
      onChange={handleChange}
      onBlur={handleBlur}
      onFocus={handleFocus}
      checkedIcon={<CheckedIcon />}
      icon={<UnCheckedIcon />}
      sx={{
        padding: '9px',
        '&:hover': {
          backgroundColor: 'rgba(39, 114, 91, 0.04)',
        },
      }}
    />
  );
};

export default CustomCheckboxWidget;
