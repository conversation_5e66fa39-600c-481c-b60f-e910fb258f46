import React from 'react';
import { FieldProps } from '@rjsf/utils';
import { Checkbox, FormControlLabel, Box } from '@mui/material';

// Custom icons for the boolean field (you can reuse the ones from FieldHOC or create new ones)
const CustomCheckedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M9.26667 10.9231L11.9333 13.6154L21 5M19 13.6154V16.8462C19 18.0357 18.0449 19 16.8667 19L7.13333 19C5.95513 19 5 18.0357 5 16.8462V7.15385C5 5.96431 5.95513 5 7.13333 5H14" stroke="#27725B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const CustomUncheckedIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M5 7V17C5 18.1046 5.89543 19 7 19H17C18.1046 19 19 18.1046 19 17V7C19 5.89543 18.1046 5 17 5H7C5.89543 5 5 5.89543 5 7Z" stroke="#27725B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const CustomBooleanField: React.FC<FieldProps> = ({
  id,
  value,
  disabled,
  readonly,
  onChange,
  onBlur,
  onFocus,
  options,
  schema,
  uiSchema,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.checked);
  };

  const handleBlur = () => {
    onBlur && onBlur(id, value);
  };

  const handleFocus = () => {
    onFocus && onFocus(id, value);
  };

  // Custom styling for the checkbox
  const checkboxStyles = {
    color: '#27725B', // Your custom color
    '&.Mui-checked': {
      color: '#27725B', // Your custom checked color
    },
    '&:hover': {
      backgroundColor: 'rgba(39, 114, 91, 0.04)', // Custom hover color
    },
    // Add any other custom styles you want
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        // Add any container styling you want
      }}
    >
      <FormControlLabel
        control={
          <Checkbox
            id={id}
            checked={value || false}
            disabled={disabled || readonly}
            onChange={handleChange}
            onBlur={handleBlur}
            onFocus={handleFocus}
            sx={checkboxStyles}
            // Use custom icons
            checkedIcon={<CustomCheckedIcon />}
            icon={<CustomUncheckedIcon />}
            // Or use default Material-UI icons
            // checkedIcon={<CheckBoxIcon />}
            // icon={<CheckBoxOutlineBlankIcon />}
          />
        }
        label="" // Empty label since the label is handled by the field template
        sx={{
          margin: 0,
          // Add any label styling you want
        }}
      />
    </Box>
  );
};

export default CustomBooleanField;
