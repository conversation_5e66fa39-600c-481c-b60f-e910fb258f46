import React, { useState, createContext, useContext, useEffect } from 'react';
import {
  FieldErrorProps,
  FieldTemplateProps,
  ObjectFieldTemplateProps,
  ArrayFieldTemplateItemType,
  ArrayFieldTemplateProps,
} from '@rjsf/utils';
import HelpIcon from '@mui/icons-material/Help';
import { IconButton, Tooltip, Box, Button } from '@mui/material';
import { ExpandLess, ExpandMore, NorthOutlined, SouthOutlined, DeleteOutlineOutlined, Add } from '@mui/icons-material';
import './InfoFieldTemplate.css';

// Context for passing array item buttons functionality
const ArrayItemContext = createContext<{
  removeButton?: React.ReactNode;
  moveUpButton?: React.ReactNode;
  moveDownButton?: React.ReactNode;
}>({});

// Context for communicating card styling needs from ObjectFieldTemplate to FieldTemplate
const CardStyleContext = createContext<{
  shouldUseCardStyle?: boolean;
  setShouldUseCardStyle?: (value: boolean) => void;
  labelInfo?: {
    label?: string;
    required?: boolean;
    description?: string;
    rawDescription?: string;
    id?: string;
  };
  setLabelInfo?: (info: any) => void;
}>({});

const customObjectStyle = {
  backgroundColor: '#F0F0F0',
  padding: '24px 24px 32px 24px',
  borderRadius:'12px'
};

const customLabelStyles = {
  fontSize: '20px',
  fontWeight: 500,  
  fontFamily: 'Roboto',
};

const customLabelTextStyles = {
  fontWeight: 400,  
  fontSize: '16px',
  fontFamily: 'Roboto',
  color: '#222229'
};

const customListItemStyles = {
  whiteSpace: 'nowrap',
};

const CustomFieldTemplate: React.FC<FieldTemplateProps> = ({
  id,
  classNames,
  label,
  required,
  description,
  errors,
  children,
  schema,
  rawDescription,
}) => {
  const descriptionForNestedProperties = description.props.description;
  const [shouldUseCardStyle, setShouldUseCardStyle] = useState(false);
  const [labelInfo, setLabelInfo] = useState(null);

  const isObjectOrArray = schema.type === 'array' || schema.type === 'object';

  // Prepare label info for the context
  const currentLabelInfo = {
    label,
    required,
    description: descriptionForNestedProperties,
    rawDescription,
    id,
  };

  console.log('CustomFieldTemplate:', {
    label,
    isObjectOrArray,
    shouldUseCardStyle,
    schemaType: schema.type
  });

  return (
    <CardStyleContext.Provider value={{
      shouldUseCardStyle,
      setShouldUseCardStyle,
      labelInfo: currentLabelInfo,
      setLabelInfo
    }}>
      <div
        style={isObjectOrArray && !shouldUseCardStyle ? customObjectStyle : null}
        className={`${classNames} ${shouldUseCardStyle ? 'card-shadow-white' : ''}`}
      >
        {/* For card style, we'll let CustomObjectFieldTemplate handle the header with buttons */}
        {/* Only render label here if it's not an object/array OR if it's an object/array without card style */}
        {(!isObjectOrArray || (isObjectOrArray && !shouldUseCardStyle)) && (
          <label
            style={isObjectOrArray ? customLabelStyles : customLabelTextStyles}
            htmlFor={id}
          >
            {label}
            {required ? <span style={{ color: 'red' }}>*</span> : null}
            {descriptionForNestedProperties !== '' ? (
              <Tooltip title={rawDescription}>
                <IconButton aria-label='info' size='small'>
                  <HelpIcon fontSize='small' />
                </IconButton>
              </Tooltip>
            ) : null}
          </label>
        )}

        <span
          className={schema?.readOnly ? 'disabled' : ''}
          title={schema.readOnly ? 'Read-only field' : ''}
        >
          {children}
        </span>
        {errors}
      </div>
    </CardStyleContext.Provider>
  );
};

const CustomObjectFieldTemplate: React.FC<ObjectFieldTemplateProps> = ({
  idSchema,
  properties,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  const { removeButton, moveUpButton, moveDownButton } = useContext(ArrayItemContext);
  const { setShouldUseCardStyle, labelInfo } = useContext(CardStyleContext);

  if (!properties.length) return null;

  const hasDirectFields = properties.some(prop => {
    const propSchema = prop.content?.props?.schema;
    return propSchema?.type && ['string', 'number', 'boolean', 'integer'].includes(propSchema.type);
  });

  const hasArrayItemButtons = moveUpButton || moveDownButton || removeButton;

  console.log('CustomObjectFieldTemplate:', {
    hasDirectFields,
    hasArrayItemButtons,
    labelInfo: labelInfo?.label
  });

  // Communicate to parent FieldTemplate whether to use card styling
  useEffect(() => {
    if (setShouldUseCardStyle && hasDirectFields && hasArrayItemButtons) {
      console.log('Setting shouldUseCardStyle to true');
      setShouldUseCardStyle(true);
    }
  }, [setShouldUseCardStyle, hasDirectFields, hasArrayItemButtons]);

  const containerClass = hasDirectFields && !hasArrayItemButtons ? 'card-shadow-white' : 'card-shadow';

  return (
    <>
      <span id={idSchema.$id} />

      {/* Combined header with label and buttons - only show when we have buttons and card styling is active */}
      {hasArrayItemButtons && hasDirectFields && labelInfo && (
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: isExpanded ? '16px' : '0',
          minHeight: '32px'
        }}>
          {/* Left side: Move buttons + Label */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {moveUpButton}
            {moveDownButton}
            <label
              style={customLabelStyles}
              htmlFor={labelInfo.id}
            >
              {labelInfo.label}
              {labelInfo.required ? <span style={{ color: 'red' }}>*</span> : null}
              {labelInfo.description !== '' ? (
                <Tooltip title={labelInfo.rawDescription}>
                  <IconButton aria-label='info' size='small'>
                    <HelpIcon fontSize='small' />
                  </IconButton>
                </Tooltip>
              ) : null}
            </label>
          </div>

          {/* Right side: Remove button and expand/collapse arrow */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            {removeButton}
            {!isExpanded && (
              <ExpandMore
                fontSize='medium'
                color='action'
                onClick={() => setIsExpanded(prev => !prev)}
              />
            )}
            {isExpanded && (
              <ExpandLess
                fontSize='medium'
                color='action'
                onClick={() => setIsExpanded(prev => !prev)}
              />
            )}
          </div>
        </div>
      )}

      {/* Legacy button positioning for non-card cases */}
      {hasArrayItemButtons && !hasDirectFields && (
        <>
          {/* Move buttons positioned on the left side */}
          {(moveUpButton || moveDownButton) && (
            <span style={{ float: 'left', display: 'flex', alignItems: 'center' }}>
              {moveUpButton}
              {moveDownButton}
            </span>
          )}

          {/* Remove button and expand/collapse arrow positioned on the right side */}
          <span style={{ float: 'right', display: 'flex', alignItems: 'center', gap: '4px' }}>
            {removeButton}
            {!isExpanded && (
              <ExpandMore
                fontSize='medium'
                color='action'
                onClick={() => setIsExpanded(prev => !prev)}
              />
            )}
            {isExpanded && (
              <ExpandLess
                fontSize='medium'
                color='action'
                onClick={() => setIsExpanded(prev => !prev)}
              />
            )}
          </span>
        </>
      )}

      {/* Expand/collapse arrow for objects without array item buttons */}
      {!hasArrayItemButtons && (
        <span style={{ float: 'right', display: 'flex', alignItems: 'center', gap: '4px' }}>
          {!isExpanded && (
            <ExpandMore
              fontSize='medium'
              color='action'
              onClick={() => setIsExpanded(prev => !prev)}
            />
          )}
          {isExpanded && (
            <ExpandLess
              fontSize='medium'
              color='action'
              onClick={() => setIsExpanded(prev => !prev)}
            />
          )}
        </span>
      )}

      {isExpanded && (
        <div className={hasDirectFields && hasArrayItemButtons ? '' : containerClass}>
          {properties.map(items => (
            <div key={items.name} className='property-wrapper field-content'>
              {items.content}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

const CustomErrorFieldTemplate: React.FC<FieldErrorProps> = ({ errors }) => {
  if (!errors?.length) return null;

  return (
    <div>
      <ul className='error-detail bs-callout bs-callout-info'>
        {errors.map(error => (
          <li
            key={error.toString()}
            className='text-danger'
            style={customListItemStyles}
          >
            {error}
          </li>
        ))}
      </ul>
    </div>
  );
};

const CustomArrayFieldItemTemplate: React.FC<ArrayFieldTemplateItemType> = ({
  children,
  disabled,
  hasRemove,
  hasMoveDown,
  hasMoveUp,
  index,
  onDropIndexClick,
  onReorderClick,
  readonly,
  className,
}) => {
  const itemStyle = {
    display: 'flex',
    alignItems: 'flex-start',
    gap: '8px',
    padding: '8px',
    // border: '1px solid #e0e0e0',
    // borderRadius: '8px',
    marginBottom: '8px',
    // backgroundColor: '#fafafa',
    position: 'relative' as const,
  };

  const buttonStyle = {
    minWidth: 'auto',
    padding: '4px',
  };


  const removeButton = hasRemove ? (
    <IconButton
      style={buttonStyle}
      onClick={onDropIndexClick(index)}
      disabled={disabled || readonly}
      size="small"
      title="Remove"
      // color="error"
    >
      <DeleteOutlineOutlined fontSize="small" sx={{ color: '#48464A' }} />
    </IconButton>
  ) : null;

  const moveUpButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveUp ? onReorderClick(index, index - 1) : undefined}
      disabled={disabled || readonly || !hasMoveUp}
      size="small"
      title="Move Up"
    >
      <NorthOutlined fontSize="small" sx={{ color: (disabled || readonly || !hasMoveUp) ? '#48464A' :  '#27725B' }} />
    </IconButton>
  );

  const moveDownButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveDown ? onReorderClick(index, index + 1) : undefined}
      disabled={disabled || readonly || !hasMoveDown}
      size="small"
      title="Move Down"
    >
      <SouthOutlined fontSize="small" sx={{ color: (disabled || readonly || !hasMoveDown) ? '#48464A' : '#27725B' }}/>
    </IconButton>
  );

  return (
    <ArrayItemContext.Provider value={{ removeButton, moveUpButton, moveDownButton }}>
      <Box style={itemStyle} className={className}>
        {/* CustomObjectFieldTemplate content with all buttons integrated in header */}
        <Box style={{ flex: 1 }}>
          {children}
        </Box>
      </Box>
    </ArrayItemContext.Provider>
  );
};

const CustomArrayFieldTemplate: React.FC<ArrayFieldTemplateProps> = ({
  canAdd,
  disabled,
  items,
  onAddClick,
  readonly,
}) => {
  const addButtonStyle = {
    marginTop: '16px',
    textTransform: 'none' as const,
    fontSize: '14px',
    padding: '8px 16px',
    borderRadius: '8px',
    backgroundColor: '#005F80',
    color: '#FAFAFA',
  };

  // console.log('ArrayFieldTemplate items:', items);

  return (
    <Box>
      {/* Array items - render each item with CustomArrayFieldItemTemplate */}
      {items && items.map((itemProps) => (
        <CustomArrayFieldItemTemplate
          key={itemProps.key}
          {...itemProps}
        />
      ))}

      {/* Add button */}
      {canAdd && (
        <Button
          style={addButtonStyle}
          onClick={onAddClick}
          disabled={disabled || readonly}
          endIcon={<Add />}
          variant="outlined"
        >
          Add Card
        </Button>
      )}
    </Box>
  );
};

export {
  CustomFieldTemplate,
  CustomObjectFieldTemplate,
  CustomErrorFieldTemplate,
  CustomArrayFieldItemTemplate,
  CustomArrayFieldTemplate,
};
