import React, { useCallback, useMemo } from 'react';
import type { WidgetProps } from '@rjsf/utils';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import IndeterminateCheckBoxIcon from '@mui/icons-material/IndeterminateCheckBox';
import { theme } from '../constants/theme';

const values = ['true', 'false', 'undefined'];

export const CheckedIcon = () => (
 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path d="M9.26667 10.9231L11.9333 13.6154L21 5M19 13.6154V16.8462C19 18.0357 18.0449 19 16.8667 19L7.13333 19C5.95513 19 5 18.0357 5 16.8462V7.15385C5 5.96431 5.95513 5 7.13333 5H14" stroke="#27725B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
);

export const UnCheckedIcon = () => (
 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path d="M5 7V17C5 18.1046 5.89543 19 7 19H17C18.1046 19 19 18.1046 19 17V7C19 5.89543 18.1046 5 17 5H7C5.89543 5 5 5.89543 5 7Z" stroke="#27725B" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
);

const IndeterminateCheckbox = (props: WidgetProps) => {
  const { disabled, name, value, onChange } = props;

  const currentIndex = useMemo(
    () =>
      values.indexOf(
        typeof value === 'undefined' ? 'undefined' : JSON.stringify(value)
      ),
    [value]
  );

  const handleOnChange = useCallback((newValue: string) => {
    if (newValue === 'undefined') {
      onChange(undefined);
      return;
    }
    onChange(JSON.parse(newValue));
  }, []);

  const handleOnClick = useCallback(() => {
    const nextIndex = (currentIndex + 1) % values.length;
    handleOnChange(values[nextIndex]);
  }, [currentIndex, handleOnChange]);

  return (
    <Button
      disabled={disabled}
      disableRipple
      onClick={handleOnClick}
      sx={{
        color: theme.palette.common.greyIcon,
        display: 'flex',
        fontWeight: 'normal',
        fontSize: '1.57rem',
        justifyContent: 'flex-start',
        textTransform: 'none',
        '&:focus': {
          outline: 'none',
        },
        '&:hover': {
          backgroundColor: 'transparent',
        },
        p: 0,
        width: '100%',
      }}
      variant='text'
    >
      <IconButton
        sx={{
          '&:focus': {
            outline: 'none',
          },
        }}
      >
        {values[currentIndex] === 'true' && <CheckedIcon/>}
        {values[currentIndex] === 'false' && <UnCheckedIcon />}
        {values[currentIndex] === 'undefined' && <IndeterminateCheckBoxIcon />}
      </IconButton>
      {name}
    </Button>
  );
};

export default IndeterminateCheckbox;
